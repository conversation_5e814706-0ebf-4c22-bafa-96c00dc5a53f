/**
 * Codebase Indexing Report Generator
 * Generates comprehensive PDF reports for codebase indexing completion
 */

import { CodebaseIndexingReport, CodebaseFileAnalysis, CodebaseIndexingReportOptions } from '../interfaces/CodebaseIndexingReport';
import { CodebaseIndexingResult } from './codebase-indexing-tool';
import { v4 as uuidv4 } from 'uuid';
import { StorageTool } from './storage-tool';
import { pdfGeneratorTool, PdfContent } from './pdf-generator';
import * as path from 'path';

export class CodebaseIndexingReportGenerator {
  private storageTool: StorageTool;

  constructor() {
    this.storageTool = new StorageTool();
  }

  /**
   * Generate a comprehensive indexing report from the indexing results
   */
  async generateReport(
    indexingResult: CodebaseIndexingResult,
    projectName: string,
    userId: string,
    fileAnalysisData: CodebaseFileAnalysis[],
    processingStartTime: number,
    options: Partial<CodebaseIndexingReportOptions> = {},
    selectedPaths?: string[]
  ): Promise<CodebaseIndexingReport> {
    const reportId = uuidv4();
    const completedAt = new Date();
    const processingTimeMs = Date.now() - processingStartTime;

    // Calculate statistics
    const totalSize = fileAnalysisData.reduce((sum, file) => sum + file.fileSize, 0);
    const averageChunkSize = totalSize / indexingResult.totalChunks;
    const processingTimePerFile = processingTimeMs / indexingResult.totalFiles;

    // Count successes and failures
    const successfulFiles = fileAnalysisData.filter(f => f.success).length;
    const failedFiles = fileAnalysisData.filter(f => !f.success).length;
    const skippedFiles = indexingResult.totalFiles - fileAnalysisData.length;

    // Collect error messages and warnings
    const errorMessages = fileAnalysisData
      .filter(f => !f.success && f.errorMessage)
      .map(f => `${f.filePath}: ${f.errorMessage}`);

    const warnings: string[] = [];
    if (skippedFiles > 0) {
      warnings.push(`${skippedFiles} files were skipped during processing`);
    }
    if (failedFiles > 0) {
      warnings.push(`${failedFiles} files failed to process completely`);
    }

    const report: CodebaseIndexingReport = {
      id: reportId,
      projectName,
      userId,
      indexingSessionId: indexingResult.documentId,
      createdAt: new Date(processingStartTime),
      completedAt,
      processingTimeMs,
      selectedPaths,
      
      statistics: {
        totalFiles: indexingResult.totalFiles,
        totalChunks: indexingResult.totalChunks,
        totalSize,
        averageChunkSize,
        processingTimePerFile
      },
      
      fileAnalysis: fileAnalysisData,
      
      vectorEmbedding: {
        pineconeNamespace: 'Codebase Documentation', // This should be dynamic based on actual namespace
        totalEmbeddings: indexingResult.totalChunks,
        embeddingModel: 'text-embedding-3-small',
        embeddingDimensions: 1536,
        indexingComplete: indexingResult.success
      },
      
      firebaseStorage: {
        reportDocumentId: reportId,
        storageComplete: false // Will be updated after PDF generation
      },
      
      summary: {
        successfulFiles,
        failedFiles,
        skippedFiles,
        errorMessages,
        warnings
      }
    };

    return report;
  }

  /**
   * Generate PDF content for the report
   * @deprecated This method is kept for backward compatibility.
   * The new PDF generation uses generatePDFSections() with the robust PDF generator.
   */
  generatePDFContent(report: CodebaseIndexingReport): string {
    const formatDate = (date: Date) => date.toLocaleString();
    const formatBytes = (bytes: number) => {
      const sizes = ['Bytes', 'KB', 'MB', 'GB'];
      if (bytes === 0) return '0 Bytes';
      const i = Math.floor(Math.log(bytes) / Math.log(1024));
      return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
    };

    const formatTime = (ms: number) => {
      if (ms < 1000) return `${ms}ms`;
      if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
      return `${(ms / 60000).toFixed(1)}m`;
    };

    return `# Codebase Indexing Completion Report

## Project Information
- **Project Name**: ${report.projectName}
- **Indexing Session ID**: ${report.indexingSessionId}
- **Report ID**: ${report.id}
- **User**: ${report.userId}
- **Started**: ${formatDate(report.createdAt)}
- **Completed**: ${formatDate(report.completedAt)}
- **Total Processing Time**: ${formatTime(report.processingTimeMs)}

## Selected Paths
${report.selectedPaths && report.selectedPaths.length > 0
  ? `**Total Selected**: ${report.selectedPaths.length} paths\n\n${report.selectedPaths.map(path => `- \`${path}\``).join('\n')}`
  : '**Note**: No specific paths were selected (full codebase scan)'
}

## Indexing Statistics
- **Total Files Processed**: ${report.statistics.totalFiles}
- **Total Chunks Generated**: ${report.statistics.totalChunks}
- **Total Codebase Size**: ${formatBytes(report.statistics.totalSize)}
- **Average Chunk Size**: ${formatBytes(report.statistics.averageChunkSize)}
- **Average Processing Time per File**: ${formatTime(report.statistics.processingTimePerFile)}

## Processing Summary
- **✅ Successful Files**: ${report.summary.successfulFiles}
- **❌ Failed Files**: ${report.summary.failedFiles}
- **⏭️ Skipped Files**: ${report.summary.skippedFiles}

## Vector Embedding Details
- **Pinecone Namespace**: ${report.vectorEmbedding.pineconeNamespace}
- **Total Embeddings Created**: ${report.vectorEmbedding.totalEmbeddings}
- **Embedding Model**: ${report.vectorEmbedding.embeddingModel}
- **Embedding Dimensions**: ${report.vectorEmbedding.embeddingDimensions}
- **Indexing Status**: ${report.vectorEmbedding.indexingComplete ? '✅ Complete' : '❌ Failed'}

## File Analysis Results

${report.fileAnalysis.map(file => `
### ${file.fileName}
- **Path**: \`${file.filePath}\`
- **Language**: ${file.language}
- **Size**: ${formatBytes(file.fileSize)}
- **Chunks**: ${file.chunkCount}
- **Processing Time**: ${formatTime(file.processingTimeMs)}
- **Status**: ${file.success ? '✅ Success' : '❌ Failed'}

**LLM Summary**: ${file.llmSummary}

**Code Analysis**:
- **Entity Type**: ${file.codeEntityType}
- **Defined Entities**: ${file.definedEntities.join(', ') || 'None'}
- **Imports**: ${file.imports.slice(0, 5).join(', ')}${file.imports.length > 5 ? ` (+${file.imports.length - 5} more)` : ''}
- **Exports**: ${file.exports.join(', ') || 'None'}
- **API Endpoints**: ${file.apiEndpoints.join(', ') || 'None'}

${file.errorMessage ? `**Error**: ${file.errorMessage}` : ''}
`).join('\n')}

## Warnings and Errors

${report.summary.warnings.length > 0 ? `
### Warnings
${report.summary.warnings.map(w => `- ⚠️ ${w}`).join('\n')}
` : ''}

${report.summary.errorMessages.length > 0 ? `
### Errors
${report.summary.errorMessages.map(e => `- ❌ ${e}`).join('\n')}
` : ''}

## Next Steps

1. **Verify Vector Store**: Check that all embeddings are accessible in Pinecone
2. **Test Semantic Search**: Perform test queries to validate the indexed content
3. **Documentation Generation**: Use Path B (PMO workflow) to generate documentation from the indexed codebase
4. **Quality Review**: Review any failed files and consider re-processing if necessary

---
*Report generated on ${formatDate(new Date())} by Codebase Indexing System*`;
  }

  /**
   * Generate structured PDF content sections for the report
   */
  private generatePDFSections(report: CodebaseIndexingReport): PdfContent[] {
    const formatDate = (date: Date) => date.toLocaleString();
    const formatBytes = (bytes: number) => {
      const sizes = ['Bytes', 'KB', 'MB', 'GB'];
      if (bytes === 0) return '0 Bytes';
      const i = Math.floor(Math.log(bytes) / Math.log(1024));
      return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
    };

    const formatTime = (ms: number) => {
      if (ms < 1000) return `${ms}ms`;
      if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
      return `${(ms / 60000).toFixed(1)}m`;
    };

    const sections: PdfContent[] = [
      // Executive Summary
      {
        title: "Executive Summary",
        content: `
## Project Overview
- **Project Name**: ${report.projectName}
- **Indexing Session ID**: ${report.indexingSessionId}
- **Report ID**: ${report.id}
- **User**: ${report.userId}
- **Started**: ${formatDate(report.createdAt)}
- **Completed**: ${formatDate(report.completedAt)}
- **Total Processing Time**: ${formatTime(report.processingTimeMs)}

## Selected Paths
${report.selectedPaths && report.selectedPaths.length > 0
  ? `**Total Selected**: ${report.selectedPaths.length} paths\n\n${report.selectedPaths.map(path => `- \`${path}\``).join('\n')}`
  : '**Note**: No specific paths were selected (full codebase scan)'
}

## Key Metrics
- **Files Processed**: ${report.statistics.totalFiles}
- **Chunks Generated**: ${report.statistics.totalChunks}
- **Codebase Size**: ${formatBytes(report.statistics.totalSize)}
- **Success Rate**: ${((report.summary.successfulFiles / report.statistics.totalFiles) * 100).toFixed(1)}%

## Status Overview
- **✅ Successful Files**: ${report.summary.successfulFiles}
- **❌ Failed Files**: ${report.summary.failedFiles}
- **⏭️ Skipped Files**: ${report.summary.skippedFiles}
- **Vector Indexing**: ${report.vectorEmbedding.indexingComplete ? '✅ Complete' : '❌ Failed'}
        `
      },

      // Detailed Statistics
      {
        title: "Indexing Statistics",
        content: `
## Processing Metrics
- **Total Files Processed**: ${report.statistics.totalFiles}
- **Total Chunks Generated**: ${report.statistics.totalChunks}
- **Total Codebase Size**: ${formatBytes(report.statistics.totalSize)}
- **Average Chunk Size**: ${formatBytes(report.statistics.averageChunkSize)}
- **Average Processing Time per File**: ${formatTime(report.statistics.processingTimePerFile)}

## Performance Analysis
- **Processing Rate**: ${(report.statistics.totalFiles / (report.processingTimeMs / 1000)).toFixed(2)} files/second
- **Chunk Generation Rate**: ${(report.statistics.totalChunks / (report.processingTimeMs / 1000)).toFixed(2)} chunks/second
- **Data Processing Rate**: ${formatBytes(report.statistics.totalSize / (report.processingTimeMs / 1000))}/second
        `
      },

      // Vector Embedding Details
      {
        title: "Vector Embedding Details",
        content: `
## Embedding Configuration
- **Pinecone Namespace**: ${report.vectorEmbedding.pineconeNamespace}
- **Total Embeddings Created**: ${report.vectorEmbedding.totalEmbeddings}
- **Embedding Model**: ${report.vectorEmbedding.embeddingModel}
- **Embedding Dimensions**: ${report.vectorEmbedding.embeddingDimensions}
- **Indexing Status**: ${report.vectorEmbedding.indexingComplete ? '✅ Complete' : '❌ Failed'}

## Vector Store Information
The codebase has been successfully indexed into a vector database for semantic search and retrieval-augmented generation (RAG). Each code chunk has been:

1. **Analyzed by LLM**: Generated intelligent summaries and metadata
2. **Embedded**: Converted to ${report.vectorEmbedding.embeddingDimensions}-dimensional vectors
3. **Indexed**: Stored in Pinecone namespace for fast retrieval
4. **Enriched**: Enhanced with code intelligence metadata

This enables powerful semantic search capabilities for documentation generation and code analysis.
        `
      }
    ];

    return sections;
  }

  /**
   * NEW: Generate multiple file analysis sections, grouped by directory.
   * This replaces the single, truncated generateFileAnalysisSection method.
   */
  private generateGroupedFileAnalysisSections(report: CodebaseIndexingReport): PdfContent[] {
    const formatBytes = (bytes: number) => {
      const sizes = ['Bytes', 'KB', 'MB', 'GB'];
      if (bytes === 0) return '0 Bytes';
      const i = Math.floor(Math.log(bytes) / Math.log(1024));
      return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
    };

    const formatTime = (ms: number) => {
      if (ms < 1000) return `${ms}ms`;
      if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
      return `${(ms / 60000).toFixed(1)}m`;
    };

    // Step 1: Group files by their parent directory
    const filesByDirectory = report.fileAnalysis.reduce((acc, file) => {
      const dir = path.dirname(file.filePath);
      if (!acc[dir]) {
        acc[dir] = [];
      }
      acc[dir].push(file);
      return acc;
    }, {} as Record<string, CodebaseFileAnalysis[]>);

    // Step 2: Create a PDF section for each directory
    const sections: PdfContent[] = [];

    // Sort directories alphabetically for consistency
    const sortedDirectories = Object.keys(filesByDirectory).sort();

    for (const directory of sortedDirectories) {
      const filesInDir = filesByDirectory[directory];

      let directoryContent = `This section details the ${filesInDir.length} file(s) found in the \`${directory}\` directory.\n\n---\n\n`;

      // Step 3: Append the full details for each file within the directory section
      directoryContent += filesInDir.map(file => `
### ${file.fileName}
- **Path**: \`${file.filePath}\`
- **Language**: ${file.language}
- **Size**: ${formatBytes(file.fileSize)}
- **Chunks Generated**: ${file.chunkCount}
- **Processing Time**: ${formatTime(file.processingTimeMs)}
- **Status**: ${file.success ? '✅ Success' : '❌ Failed'}

**LLM Analysis Summary**:
${file.llmSummary}

**Code Intelligence**:
- **Entity Type**: ${file.codeEntityType}
- **Defined Entities**: ${file.definedEntities.length > 0 ? file.definedEntities.join(', ') : 'None detected'}
- **Imports**: ${file.imports.length > 0 ? file.imports.join(', ') : 'None'}
- **Exports**: ${file.exports.length > 0 ? file.exports.join(', ') : 'None'}
- **API Endpoints**: ${file.apiEndpoints.length > 0 ? file.apiEndpoints.join(', ') : 'None'}

${file.errorMessage ? `**Error Details**: ${file.errorMessage}` : ''}

---
      `).join('\n');

      sections.push({
        // Use directory path as the title for the section in the TOC
        title: `Directory: ${directory}`,
        content: directoryContent
      });
    }

    return sections;
  }



  /**
   * Generate warnings and errors section for PDF
   */
  private generateWarningsAndErrorsSection(report: CodebaseIndexingReport): PdfContent {
    let content = '';

    if (report.summary.warnings.length > 0) {
      content += `## Warnings\n\n`;
      content += report.summary.warnings.map(warning => `⚠️ ${warning}`).join('\n\n');
      content += '\n\n';
    }

    if (report.summary.errorMessages.length > 0) {
      content += `## Errors\n\n`;
      content += report.summary.errorMessages.map(error => `❌ ${error}`).join('\n\n');
      content += '\n\n';
    }

    if (report.summary.failedFiles > 0) {
      content += `## Failed Files Analysis\n\n`;
      const failedFiles = report.fileAnalysis.filter(f => !f.success);

      content += `**Total Failed Files**: ${failedFiles.length}\n\n`;

      if (failedFiles.length > 0) {
        content += `### Failed Files Details:\n\n`;
        failedFiles.forEach(file => {
          content += `- **${file.fileName}** (\`${file.filePath}\`)\n`;
          content += `  - Language: ${file.language}\n`;
          content += `  - Error: ${file.errorMessage || 'Unknown error'}\n\n`;
        });
      }
    }

    content += `## Troubleshooting Recommendations\n\n`;

    if (report.summary.failedFiles > 0) {
      content += `1. **Review Failed Files**: Check the error messages above and consider:\n`;
      content += `   - File encoding issues (ensure UTF-8 encoding)\n`;
      content += `   - File size limitations\n`;
      content += `   - Syntax errors in source files\n`;
      content += `   - Permission issues\n\n`;
    }

    if (report.summary.skippedFiles > 0) {
      content += `2. **Skipped Files**: ${report.summary.skippedFiles} files were skipped. Common reasons:\n`;
      content += `   - Binary files (images, executables, etc.)\n`;
      content += `   - Files exceeding size limits\n`;
      content += `   - Unsupported file types\n`;
      content += `   - Files in ignored directories\n\n`;
    }

    content += `3. **Performance Considerations**:\n`;
    content += `   - Monitor processing times for large files\n`;
    content += `   - Consider chunking strategy adjustments\n`;
    content += `   - Review LLM analysis timeouts\n\n`;

    content += `4. **Quality Assurance**:\n`;
    content += `   - Validate LLM-generated summaries\n`;
    content += `   - Check code entity classifications\n`;
    content += `   - Test semantic search functionality\n`;

    return {
      title: "Warnings, Errors & Troubleshooting",
      content
    };
  }

  /**
   * Save the report to Firebase and generate actual PDF
   */
  async saveReport(report: CodebaseIndexingReport): Promise<string> {
    try {
      console.log(`📄 Generating PDF report for project: ${report.projectName}`);

      // Generate structured PDF content sections
      const pdfSections = this.generatePDFSections(report);

      // **THE CRITICAL CHANGE IS HERE**
      // Call the new grouping function to get all file details
      if (report.fileAnalysis.length > 0) {
        const fileSections = this.generateGroupedFileAnalysisSections(report);
        pdfSections.push(...fileSections); // Add all the grouped file sections
      }

      // Add warnings and errors section if there are any
      if (report.summary.warnings.length > 0 || report.summary.errorMessages.length > 0) {
        pdfSections.push(this.generateWarningsAndErrorsSection(report));
      }

      // Add next steps section
      pdfSections.push({
        title: "Next Steps & Recommendations",
        content: `
## Recommended Actions

1. **Verify Vector Store Access**
   - Test semantic search queries against the indexed content
   - Validate that all embeddings are accessible in Pinecone namespace: \`${report.vectorEmbedding.pineconeNamespace}\`

2. **Quality Assurance**
   - Review any failed files and consider re-processing if necessary
   - Validate LLM-generated summaries for accuracy
   - Check code entity classifications

3. **Documentation Generation**
   - Use Path B (PMO workflow) to generate comprehensive documentation
   - Leverage the enriched metadata for better documentation quality
   - Test retrieval-augmented generation capabilities

4. **Performance Optimization**
   - Monitor query performance against the vector store
   - Consider chunking strategy adjustments for future indexing
   - Review processing time metrics for optimization opportunities

## System Integration
The indexed codebase is now ready for:
- **Semantic Code Search**: Find relevant code sections using natural language queries
- **Documentation Generation**: Automated creation of technical documentation
- **Code Analysis**: AI-powered insights and recommendations
- **Knowledge Base Queries**: Intelligent Q&A about the codebase
        `
      });

      // Generate PDF using the robust PDF generator
      const fileName = `codebase-indexing-report-${report.projectName.replace(/[^a-zA-Z0-9]/g, '_')}-${report.id}.pdf`;

      const pdfResult = await pdfGeneratorTool.generatePdf(pdfSections, {
        title: `Codebase Indexing Report: ${report.projectName}`,
        subtitle: `Completion Confirmation & Analysis`,
        date: report.completedAt.toLocaleDateString(),
        includeCover: true,
        includeToc: true,
        saveToByteStore: false, // We'll handle storage manually
        category: 'Codebase Indexing',
        documentType: 'Indexing Report',
        author: 'Codebase Indexing System',
        generatedBy: 'CodebaseIndexingSystem',
        reportId: report.id,
        reportType: 'Codebase Indexing Completion'
      });

      // Ensure we have a Buffer
      const pdfBuffer = Buffer.isBuffer(pdfResult) ? pdfResult : Buffer.from(pdfResult as any);

      // Save PDF to Firebase storage
      const storagePath = `codebase-indexing-reports/${report.userId}`;

      const pdfUrl = await this.storageTool.saveToStorage(
        pdfBuffer,
        storagePath,
        fileName,
        {
          contentType: 'application/pdf',
          customMetadata: {
            projectName: report.projectName,
            reportId: report.id,
            generatedBy: 'CodebaseIndexingSystem',
            reportType: 'codebase-indexing-completion',
            totalFiles: report.statistics.totalFiles.toString(),
            totalChunks: report.statistics.totalChunks.toString(),
            processingTimeMs: report.processingTimeMs.toString()
          }
        }
      );

      // Update report with PDF URL
      report.firebaseStorage.reportPdfUrl = pdfUrl;
      report.firebaseStorage.storageComplete = true;

      // Save report metadata to Firebase
      const reportData = {
        ...report,
        createdAt: report.createdAt.toISOString(),
        completedAt: report.completedAt.toISOString()
      };

      await this.storageTool.saveToFirestore(
        reportData,
        `users/${report.userId}/codebase-indexing-reports`
      );

      console.log(`✅ Codebase indexing PDF report saved: ${fileName}`);
      console.log(`📊 Report statistics: ${report.statistics.totalFiles} files, ${report.statistics.totalChunks} chunks`);

      return pdfUrl;
    } catch (error) {
      console.error('❌ Failed to save codebase indexing PDF report:', error);
      throw error;
    }
  }
}

export const codebaseIndexingReportGenerator = new CodebaseIndexingReportGenerator();
